#!/bin/bash

# 专门解决MoE通信缓冲区问题的启动脚本
set -e

MODEL_PATH="/models/DeepSeek-R1-0528"
NODE_TYPE=${1:-"single"}

echo "=== MoE通信问题修复版启动 ==="
echo "节点类型: $NODE_TYPE"
echo "时间: $(date)"

# 强制禁用可能导致问题的特性
export VLLM_USE_V1=0
export VLLM_ENABLE_V1_MULTIPROCESSING=0
export VLLM_DISABLE_ASYNC_OUTPUT_PROC=1

# 禁用专家并行（这是关键！）
# 注意：VLLM_DISABLE_EXPERT_PARALLEL环境变量不存在，需要通过命令行参数禁用
# export VLLM_DISABLE_EXPERT_PARALLEL=1  # 这个环境变量无效！

# DeepGEMM配置 - 保守设置
export VLLM_ALL2ALL_BACKEND="nccl"  # 改用标准NCCL
export VLLM_USE_DEEP_GEMM=0  # 暂时禁用DeepGEMM

# 日志配置
export VLLM_LOGGING_LEVEL=DEBUG
export PYTHONUNBUFFERED=1

# NCCL配置 - 更保守的设置
export NCCL_DEBUG=INFO
export NCCL_TIMEOUT=7200
export NCCL_SOCKET_TIMEOUT=7200

# 禁用可能导致通信问题的特性
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1
export NCCL_NET_GDR_LEVEL=0
export NCCL_TREE_THRESHOLD=0

# 强制使用CPU进行某些操作
export NCCL_ALGO=Tree
export NCCL_PROTO=Simple

# MoE相关环境变量
export VLLM_MOE_PADDING=1
export VLLM_DISABLE_CUSTOM_ALL_REDUCE=1

echo "环境变量已设置（专家并行已禁用）"

# 检查模型路径
if [ ! -d "$MODEL_PATH" ]; then
    echo "错误: 模型路径不存在: $MODEL_PATH"
    exit 1
fi

# 清理函数
cleanup() {
    echo "清理进程..."
    pkill -f "vllm serve" || true
    sleep 2
}

trap cleanup EXIT

if [ "$NODE_TYPE" = "single" ]; then
    echo "单节点模式启动（禁用专家并行）..."
    
    cleanup
    
    vllm serve "$MODEL_PATH" \
        --trust-remote-code \
        --tensor-parallel-size 4 \
        --no-enable-expert-parallel \
        --quantization fp8 \
        --dtype bfloat16 \
        --max-model-len 8192 \
        --max-num-seqs 16 \
        --disable-log-requests \
        --gpu-memory-utilization 0.8 \
        --port 8000 \
        --host 0.0.0.0 \
        --served-model-name deepseek-r1 \
        --disable-frontend-multiprocessing \
        --enforce-eager \
        --no-enable-chunked-prefill \
        --no-enable-prefix-caching \
        2>&1 | tee vllm_moe_single.log
        
elif [ "$NODE_TYPE" = "minimal" ]; then
    echo "最小配置模式（完全禁用并行特性）..."
    
    cleanup
    
    vllm serve "$MODEL_PATH" \
        --trust-remote-code \
        --tensor-parallel-size 2 \
        --no-enable-expert-parallel \
        --dtype bfloat16 \
        --max-model-len 4096 \
        --max-num-seqs 8 \
        --gpu-memory-utilization 0.7 \
        --port 8000 \
        --host 0.0.0.0 \
        --disable-frontend-multiprocessing \
        --enforce-eager \
        2>&1 | tee vllm_moe_minimal.log
        
elif [ "$NODE_TYPE" = "master" ]; then
    echo "主节点启动（禁用专家并行）..."
    
    cleanup
    
    # 检查端口
    if netstat -tulpn 2>/dev/null | grep -q ":13345 "; then
        echo "错误: 端口 13345 已被占用"
        exit 1
    fi
    
    vllm serve "$MODEL_PATH" \
        --trust-remote-code \
        --tensor-parallel-size 4 \
        --data-parallel-size 2 \
        --data-parallel-address ********** \
        --data-parallel-rpc-port 13345 \
        --disable-custom-all-reduce \
        --quantization fp8 \
        --dtype bfloat16 \
        --max-model-len 8192 \
        --max-num-seqs 16 \
        --disable-log-requests \
        --enforce-eager \
        --no-enable-chunked-prefill \
        --no-enable-prefix-caching \
        --gpu-memory-utilization 0.8 \
        --port 8000 \
        --host 0.0.0.0 \
        --disable-frontend-multiprocessing \
        2>&1 | tee vllm_moe_master.log
        
elif [ "$NODE_TYPE" = "worker" ]; then
    echo "从节点启动（禁用专家并行）..."
    
    # 等待主节点
    echo "等待主节点启动..."
    max_wait=300
    wait_time=0
    
    while ! nc -z ********** 13345; do
        if [ $wait_time -ge $max_wait ]; then
            echo "错误: 等待主节点超时"
            exit 1
        fi
        echo "等待主节点... ($wait_time/$max_wait 秒)"
        sleep 5
        wait_time=$((wait_time + 5))
    done
    
    echo "主节点已就绪，启动从节点..."
    sleep 20
    
    vllm serve "$MODEL_PATH" \
        --trust-remote-code \
        --tensor-parallel-size 4 \
        --data-parallel-size 2 \
        --data-parallel-start-rank 1 \
        --data-parallel-address ********** \
        --data-parallel-rpc-port 13345 \
        --disable-custom-all-reduce \
        --quantization fp8 \
        --dtype bfloat16 \
        --max-model-len 8192 \
        --max-num-seqs 16 \
        --disable-log-requests \
        --enforce-eager \
        --no-enable-chunked-prefill \
        --no-enable-prefix-caching \
        --headless \
        --gpu-memory-utilization 0.8 \
        2>&1 | tee vllm_moe_worker.log
        
else
    echo "用法: $0 [single|minimal|master|worker]"
    echo "  single  - 单节点模式（禁用专家并行）"
    echo "  minimal - 最小配置模式"
    echo "  master  - 主节点（禁用专家并行）"
    echo "  worker  - 从节点（禁用专家并行）"
    exit 1
fi
