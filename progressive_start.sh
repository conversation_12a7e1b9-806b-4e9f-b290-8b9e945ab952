请不要修改DeepEP的源代码。我需要通过修改容器配置来满足DeepEP低延迟模式的运行要求。

具体需求：
1. 访问DeepEP的GitHub仓库 (https://github.com/deepseek-ai/DeepEP)，查找关于容器化部署的官方文档和配置要求
2. 重点关注以下方面的配置需求：
   - 容器需要挂载哪些主机设备和目录来支持InfiniBand/RDMA
   - NVSHMEM在容器环境中的正确配置方法
   - nvidia-peermem和GPU Direct RDMA的容器支持配置
   - 低延迟模式(low_latency_mode=True)的特殊容器要求

3. 基于找到的信息，提供具体的容器启动命令或Docker配置，解决当前程序卡在NVSHMEM初始化阶段的问题

背景：我在容器内运行DeepEP测试时，程序卡在`internode::init()`的NVSHMEM初始化步骤，主机有InfiniBand硬件但容器无法正确访问。需要找到官方推荐的容器配置方法。